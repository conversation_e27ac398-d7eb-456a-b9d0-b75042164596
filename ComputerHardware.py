import json
import logging
import os
from datetime import datetime, timezone, timedelta

import requests
import wmi
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header

from apis.api import GetDiskInfo, CreateDiskInfo
from apis.constant import http_https, localhost, localport
from funPulic import before_bc_3_Date, before_bc_10_Date, before_bc_30_Date, before_bc_90_Date
from setting import sender, recipients, smtp_server, smtp_port, ip_to_computer_name_map_1, ip_to_computer_name_map_2


# recipients = ["<EMAIL>"]  # 收件人列表，可以添加多個





# ============================================================
# ↓↓↓↓↓↓↓↓↓↓↓↓↓ Helper 函式，可放在類別外或用 @staticmethod ↓↓↓
# ============================================================
def _append_record(buf, drive_letter, bytes_total, bytes_free, label, ip, GB, TB):
    """格式化容量並加入 Data 陣列。"""
    # 跳過超過兩個字符的驅動器
    if len(drive_letter) > 2:
        return

    if bytes_total == 0:
        return
    if bytes_total / GB > 1024:
        unit, total_f, free_f = "TB", bytes_total / TB, bytes_free / TB
    else:
        unit, total_f, free_f = "GB", bytes_total / GB, bytes_free / GB
    used_f = max(0, total_f - free_f)
    rate = int((used_f / total_f) * 100)
    buf.append({
        "DnsName": ip,
        "Caption": drive_letter,
        "CaptionName": label,
        "DiskUnit": unit,
        "DiskTotal": f"{total_f:.3f}",
        "UseSpace": f"{free_f:.3f}",  # 可用空間
        "FreeSpace": f"{used_f:.3f}",  # 已使用空間
        "Rate": f"{rate}%",
    })


def _calc_real_size(cim, drive_letter):
    """
    透過關聯表把邏輯磁碟對應到所有分割區，再把每個 Partition.Size 加總。
    適用於 Win32_* 類別回傳 2 GB 錯誤時的補救。
    """
    total = 0
    logic = cim.Win32_LogicalDisk(DeviceID=drive_letter)
    if not logic:
        return 0
    for part in logic[0].associators(wmi_result_class="Win32_DiskPartition"):
        if part.Size:
            total += int(part.Size)
    return total


class WindowsListen():
    # 靜態屬性，用於存儲所有伺服器資訊 (無論是否有警告)
    servers_info = []  # 存儲所有伺服器資訊
    servers_alert_info = []  # 只存儲有警告的伺服器
    pending_alerts_count = 0  # 追蹤待處理的警告數量
    max_servers_before_alert = 1  # 設定一個閾值，達到此數量就發送警告

    # 電腦資訊
    def getComputerSystemInfo(self, ip, user, password):
        strIP, strUser, strPassword = ip, user, password

        # 初始化返回JSON格式
        Ret = {"Code": "0", "Msg": "OK", "Data": []}

        # 連線服務器
        try:
            getData = wmi.WMI(computer=strIP, user=strUser, password=strPassword)
        except Exception as e:
            Ret["code"] = "-1"
            Ret["msg"] = "服務器 : " + strIP + "連接失敗！" + str(e)
            return json.dumps(Ret, ensure_ascii=False)

        Data = []

        try:
            queryWin32_ComputerSystem = getData.query("Select * from Win32_OperatingSystem")

            if not queryWin32_ComputerSystem:
                Ret["Code"] = "-1"
                Ret["Msg"] = f"無法獲取 {strIP} 的作業系統資訊"
                return json.dumps(Ret, ensure_ascii=False)

        except Exception as e:
            Ret["Code"] = "-1"
            Ret["Msg"] = f"服務器 : {strIP} 查詢失敗！{str(e)}"
            return json.dumps(Ret, ensure_ascii=False)

        for data in queryWin32_ComputerSystem:
            try:
                TmpDict = {}
                GB = 1024 ** 2

                # 主機名稱
                TmpDict["CSName"] = data.CSName
                # 作業系統名稱
                TmpDict["Caption"] = data.Caption
                # 作業系統版本
                TmpDict["BuildNumber"] = data.BuildNumber
                # 記憶體單位
                TmpDict["MemoryUnit"] = 'GB'

                # 計算記憶體資訊
                total_memory_kb = int(data.TotalVisibleMemorySize)
                free_memory_kb = int(data.FreePhysicalMemory)
                used_memory_kb = total_memory_kb - free_memory_kb

                # 總計記憶體
                TmpDict["TotalVisibleMemorySize"] = format(total_memory_kb / GB, '.2f')
                # 可使用記憶體
                TmpDict["FreePhysicalMemorySize"] = format(free_memory_kb / GB, '.2f')
                # 已使用記憶體
                TmpDict["UsePhysicalMemorySize"] = format(used_memory_kb / GB, '.2f')
                # 使用率
                memory_usage_rate = (used_memory_kb / total_memory_kb) * 100
                TmpDict["Rate"] = format(memory_usage_rate, '.2f') + '%'

                Data.append(TmpDict)

            except Exception as e:
                continue

        print('Data', Data)

        Ret["Data"] = Data
        return json.dumps(Ret, ensure_ascii=False)

    def sendDiskSpaceAlert(self, threshold):
        """
        發送電子郵件通知所有伺服器的硬碟使用情況與變化

        :param threshold: 空間閾值(百分比)
        :return: 是否成功發送
        """
        try:
            # 檢查是否有任何伺服器資訊
            if not WindowsListen.servers_info:
                print(f"沒有收集到任何伺服器資訊，無需發送郵件")
                return True

            # 篩選有警告的伺服器
            alert_servers = [server for server in WindowsListen.servers_info if server.get("alert_disks")]

            # 獲取當前時間作為郵件標識
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 創建郵件
            msg = MIMEMultipart()
            msg['From'] = sender
            msg['To'] = ', '.join(recipients)

            # 根據伺服器狀態決定郵件主旨
            if alert_servers:
                if len(alert_servers) == 1:
                    server_info = alert_servers[0]
                    subject = f'【警告】伺服器 {server_info["computer_name"]} ({server_info["ip"]}) 硬碟空間不足'
                else:
                    subject = f'【警告】多台伺服器硬碟空間不足 ({len(alert_servers)}台)'
            else:
                subject = f'伺服器硬碟使用情況報告 ({len(WindowsListen.servers_info)}台)'

            msg['Subject'] = Header(subject, 'utf-8').encode()

            # 郵件正文開始
            body = f"""<!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
            </head>
            <body style="font-family: Arial, 'Microsoft JhengHei', sans-serif; margin: 0; padding: 15px; font-size: 14px; color: #333333;">
                <div style="margin: 0; padding: 0;">
            """

            # 如果有警告的伺服器，添加警告標題
            if alert_servers:
                body += f"""
                    <h2 style="color: #c00000; border-bottom: 2px solid #c00000; padding-bottom: 8px; font-size: 18px; margin-top: 0;">伺服器硬碟空間警告</h2>
                    <p style="margin-bottom: 15px;">以下伺服器的硬碟空間低於閾值(<strong>{threshold}%</strong>)，請盡快處理：</p>
                """
            else:
                body += f"""
                    <h2 style="color: #0066cc; border-bottom: 2px solid #0066cc; padding-bottom: 8px; font-size: 18px; margin-top: 0;">伺服器硬碟使用情況報告</h2>
                    <p style="margin-bottom: 15px;">以下是所有伺服器的硬碟使用情況與24小時變化：</p>
                """

            # 首先處理有警告的伺服器
            for server_info in alert_servers:
                ip = server_info["ip"]
                computer_name = server_info["computer_name"]
                alert_disks = server_info["alert_disks"]
                all_disks = server_info.get("all_disks", [])

                # 添加伺服器標題區塊
                body += f"""
                    <div style="margin-bottom: 30px;">
                        <h3 style="margin-top: 0; margin-bottom: 15px; color: #333333; font-size: 16px; background-color: #f0f0f0; padding: 8px; border-left: 4px solid #0078d4;">伺服器: {computer_name} ({ip}) - 有警告</h3>
                """

                # 第一部分：警告磁碟 - 只有在有警告時才顯示
                if alert_disks:
                    # 對警告磁碟按照代號排序
                    alert_disks_sorted = sorted(alert_disks, key=lambda x: x["Caption"])

                    body += f"""
                        <div style="margin-bottom: 20px; background-color: #fff8f8; padding: 10px; border-left: 4px solid #c00000;">
                            <h4 style="color: #c00000; margin-top: 0; margin-bottom: 10px;">⚠️ 警告磁碟</h4>
                            <table border="1" cellpadding="6" cellspacing="0" style="border-collapse: collapse; width: 100%; border: 2px solid #999999;">
                                <tr style="background-color: #e0e0e0;">
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">磁碟機</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">標籤</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">單位</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">總容量</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">可用空間</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">已使用空間</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">使用率</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">警告等級</th>
                                </tr>
                    """

                    # 添加警告磁碟列表 (已排序)
                    for disk in alert_disks_sorted:
                        # 解析使用率以決定警告等級
                        rate = int(disk["Rate"].strip('%'))
                        free_percent = 100 - rate

                        # 根據可用空間決定背景色和警告級別
                        if free_percent < 5:
                            bg_color = "#ffd0d0"  # 嚴重 - 深紅色
                            warning_level = "嚴重"
                        else:
                            bg_color = "#fff0f0"  # 警告 - 淺紅色
                            warning_level = "警告"

                        body += f"""
                                <tr style="background-color: {bg_color};">
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["Caption"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["CaptionName"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["DiskUnit"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["DiskTotal"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["UseSpace"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["FreeSpace"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold; color: #c00000;">{disk["Rate"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold; color: #c00000;">{warning_level}</td>
                                </tr>
                        """

                    body += """
                            </table>
                        </div>
                    """

                # 第二部分：所有磁碟使用量變化 - 無論是否有警告都顯示
                if all_disks:
                    # 對所有磁碟按照代號排序
                    all_disks_sorted = sorted(all_disks, key=lambda x: x["Caption"])

                    body += f"""
                        <div style="margin-bottom: 20px; background-color: #f6f9ff; padding: 10px; border-left: 4px solid #0066cc;">
                            <h4 style="color: #0066cc; margin-top: 0; margin-bottom: 10px;">💾 所有磁碟使用量變化（24小時比較）</h4>
                            <table border="1" cellpadding="6" cellspacing="0" style="border-collapse: collapse; width: 100%; border: 2px solid #999999;">
                                <tr style="background-color: #e0e0e0;">
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">磁碟機</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">標籤</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">單位</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">總容量</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">可用空間</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">已使用空間</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">使用率</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">24小時變化</th>
                                    <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">變化趨勢</th>
                                </tr>
                    """

                    # 添加所有磁碟及其變化 (已排序)
                    for disk in all_disks_sorted:
                        # 解析使用率以決定顯示樣式
                        try:
                            rate = int(disk["Rate"].strip('%'))
                            free_percent = 100 - rate
                        except:
                            rate = 0
                            free_percent = 100

                        # 設定背景色
                        if free_percent < 5:  # 嚴重警告
                            bg_color = "#ffd0d0"
                        elif free_percent < threshold:  # 警告
                            bg_color = "#fff0f0"
                        else:  # 正常
                            bg_color = "#ffffff"

                        # 取得變化資訊
                        change_size = disk.get('ChangeSize', 'N/A')
                        change_percent = disk.get('ChangePercent', 'N/A')
                        trend = disk.get('Trend', '無法比較')

                        # 根據趨勢設定顯示樣式
                        if trend == '增加':
                            trend_display = '<span style="color: #c00000; font-weight: bold;">↑ 增加</span>'
                        elif trend == '減少':
                            trend_display = '<span style="color: #008000; font-weight: bold;">↓ 減少</span>'
                        elif trend == '無變化':
                            trend_display = '<span style="color: #666666;">→ 無變化</span>'
                        else:
                            trend_display = f'<span style="color: #666666;">- {trend}</span>'

                        # 變化量顯示
                        if change_size != 'N/A' and change_percent != 'N/A':
                            change_display = f"{change_size} {disk['DiskUnit']} ({change_percent})"
                        else:
                            change_display = "無前日資料"

                        body += f"""
                                <tr style="background-color: {bg_color};">
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["Caption"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["CaptionName"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["DiskUnit"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["DiskTotal"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["UseSpace"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["FreeSpace"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["Rate"]}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{change_display}</td>
                                    <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{trend_display}</td>
                                </tr>
                        """

                    body += """
                            </table>
                        </div>
                    """
                elif not all_disks:
                    # 如果沒有任何磁碟資訊，顯示提示訊息
                    body += """
                        <div style="margin-bottom: 20px; background-color: #f6f9ff; padding: 10px; border-left: 4px solid #0066cc;">
                            <p style="color: #666666; font-style: italic;">此伺服器沒有磁碟資訊。</p>
                        </div>
                    """

                # 結束伺服器區塊
                body += """
                    </div>
                """

            # 然後處理沒有警告的伺服器
            normal_servers = [server for server in WindowsListen.servers_info if
                              not server.get("alert_disks") and server not in alert_servers]

            if normal_servers:
                body += f"""
                    <h2 style="margin-top: 30px; color: #0066cc; border-bottom: 2px solid #0066cc; padding-bottom: 8px; font-size: 18px;">正常伺服器硬碟使用情況</h2>
                    <p style="margin-bottom: 15px;">以下伺服器的硬碟空間正常：</p>
                """

                for server_info in normal_servers:
                    ip = server_info["ip"]
                    computer_name = server_info["computer_name"]
                    all_disks = server_info.get("all_disks", [])

                    # 添加伺服器標題區塊
                    body += f"""
                        <div style="margin-bottom: 30px;">
                            <h3 style="margin-top: 0; margin-bottom: 15px; color: #333333; font-size: 16px; background-color: #f0f0f0; padding: 8px; border-left: 4px solid #00aa00;">伺服器: {computer_name} ({ip}) - 正常</h3>
                    """

                    # 顯示所有磁碟使用量變化
                    if all_disks:
                        # 對所有磁碟按照代號排序
                        all_disks_sorted = sorted(all_disks, key=lambda x: x["Caption"])

                        body += f"""
                            <div style="margin-bottom: 20px; background-color: #f6f9ff; padding: 10px; border-left: 4px solid #0066cc;">
                                <h4 style="color: #0066cc; margin-top: 0; margin-bottom: 10px;">💾 所有磁碟使用量變化（24小時比較）</h4>
                                <table border="1" cellpadding="6" cellspacing="0" style="border-collapse: collapse; width: 100%; border: 2px solid #999999;">
                                    <tr style="background-color: #e0e0e0;">
                                        <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">磁碟機</th>
                                        <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">標籤</th>
                                        <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">單位</th>
                                        <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">總容量</th>
                                        <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">可用空間</th>
                                        <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">已使用空間</th>
                                        <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">使用率</th>
                                        <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">24小時變化</th>
                                        <th style="border: 1px solid #999999; padding: 6px; text-align: center; font-weight: bold;">變化趨勢</th>
                                    </tr>
                        """

                        # 添加所有磁碟及其變化 (已排序)
                        for disk in all_disks_sorted:
                            # 解析使用率以決定顯示樣式
                            try:
                                rate = int(disk["Rate"].strip('%'))
                                free_percent = 100 - rate
                            except:
                                rate = 0
                                free_percent = 100

                            # 設定背景色 (正常伺服器都是綠色或白色)
                            bg_color = "#ffffff"  # 正常

                            # 取得變化資訊
                            change_size = disk.get('ChangeSize', 'N/A')
                            change_percent = disk.get('ChangePercent', 'N/A')
                            trend = disk.get('Trend', '無法比較')

                            # 根據趨勢設定顯示樣式
                            if trend == '增加':
                                trend_display = '<span style="color: #c00000; font-weight: bold;">↑ 增加</span>'
                            elif trend == '減少':
                                trend_display = '<span style="color: #008000; font-weight: bold;">↓ 減少</span>'
                            elif trend == '無變化':
                                trend_display = '<span style="color: #666666;">→ 無變化</span>'
                            else:
                                trend_display = f'<span style="color: #666666;">- {trend}</span>'

                            # 變化量顯示
                            if change_size != 'N/A' and change_percent != 'N/A':
                                change_display = f"{change_size} {disk['DiskUnit']} ({change_percent})"
                            else:
                                change_display = "無前日資料"

                            body += f"""
                                    <tr style="background-color: {bg_color};">
                                        <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["Caption"]}</td>
                                        <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["CaptionName"]}</td>
                                        <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["DiskUnit"]}</td>
                                        <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["DiskTotal"]}</td>
                                        <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["UseSpace"]}</td>
                                        <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["FreeSpace"]}</td>
                                        <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{disk["Rate"]}</td>
                                        <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{change_display}</td>
                                        <td style="border: 1px solid #999999; padding: 6px; text-align: center;">{trend_display}</td>
                                    </tr>
                            """

                        body += """
                                </table>
                            </div>
                        """
                    elif not all_disks:
                        # 如果沒有任何磁碟資訊，顯示提示訊息
                        body += """
                            <div style="margin-bottom: 20px; background-color: #f6f9ff; padding: 10px; border-left: 4px solid #0066cc;">
                                <p style="color: #666666; font-style: italic;">此伺服器沒有磁碟資訊。</p>
                            </div>
                        """

                    # 結束伺服器區塊
                    body += """
                        </div>
                    """

            # 郵件正文結尾
            footer_text = "請盡快處理警告磁碟以確保系統正常運行。" if alert_servers else "所有伺服器硬碟空間正常。"
            body += f"""
                    <p style="margin-top: 15px;">{footer_text}</p>
                    <p style="margin-top: 20px; padding-top: 10px; border-top: 1px solid #cccccc; font-size: 12px; color: #777777;">此郵件由系統自動發送於 {current_time}，請勿回覆。</p>
                </div>
            </body>
            </html>
            """

            msg.attach(MIMEText(body, 'html', 'utf-8'))

            # 連接到郵件服務器並發送
            print('smtp_server', smtp_server, 'smtp_port', smtp_port)
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.send_message(msg)

            print(f"已成功發送硬碟空間報告郵件給 {', '.join(recipients)}")
            logging.info(f"已成功發送硬碟空間報告郵件給 {', '.join(recipients)}")
            return True

        except Exception as e:
            print(f"發送電子郵件時發生錯誤: {str(e)}")
            logging.error(f"發送電子郵件時發生錯誤: {str(e)}")
            return False

    # 收集所有伺服器資訊 (包括無警告的)
    def collectServerInfo(self, alert_threshold=20):
        """
        收集並處理所有伺服器資訊 (無論是否有警告)

        :param alert_threshold: 警告閾值
        :return: 收集到的伺服器數量
        """
        # 已經收集完成，直接返回
        return len(WindowsListen.servers_info)

    # 發送所有收集的警告
    def sendAllServerInfo(self, alert_threshold=20):
        """
        發送所有收集的伺服器資訊

        :param alert_threshold: 警告閾值
        :return: 是否成功發送
        """
        # 檢查是否有任何伺服器資訊
        if WindowsListen.servers_info:
            # 發送包含所有伺服器的郵件
            success = self.sendDiskSpaceAlert(alert_threshold)

            # 發送後清空列表
            if success:
                WindowsListen.servers_info = []
                WindowsListen.servers_alert_info = []
                WindowsListen.pending_alerts_count = 0
            return success
        else:
            print("沒有收集到任何伺服器資訊，無需發送郵件")
            return True

    # ------------------------------------------------------------
    # 硬碟資訊 – 最終改進版：動態磁碟 / 5 TB 以上可精準顯示
    # ------------------------------------------------------------
    def getDiskInfo(self, ip, user, password, alert_threshold=20, send_alert=False):
        """
        取得目標 Windows 伺服器之磁碟使用狀態；可自動寄送容量不足警示

        :param ip:  伺服器 IP
        :param user: 使用者名稱
        :param password: 密碼
        :param alert_threshold: 當剩餘空間百分比 < 此值即列入警示
        :param send_alert: 是否立即寄送警示 (設為False時只加入清單不寄送)
        :return: JSON 字串
        """
        strIP, strUser, strPassword = ip, user, password
        Ret = {"Code": "0", "Msg": "OK", "Data": []}

        # ---------------- 1. IP ↔︎ 電腦名稱 ----------------
        computer_name = ip_to_computer_name_map_1.get(strIP, f"{strIP}.heysong.com.tw")

        # ---------------- 2. 建立 WMI 連線 ----------------
        try:
            cimv2 = wmi.WMI(computer=strIP, user=strUser, password=strPassword)
            try:
                storage = wmi.WMI(moniker=f"//{strIP}/root/Microsoft/Windows/Storage")
            except wmi.x_wmi:
                storage = None  # 舊系統沒有 Storage namespace
        except Exception as e:
            Ret.update(Code="-1", Msg=f"伺服器 {strIP} 連線失敗！{e}")
            return json.dumps(Ret, ensure_ascii=False)

        GB, TB = 1024 ** 3, 1024 ** 4
        Data = []

        # ---------------- 3‑A. 先試 MSFT_Volume ----------------
        got_data = False
        if storage:
            try:
                for v in storage.MSFT_Volume(DriveType=3):  # 固定磁碟
                    _append_record(Data, v.DriveLetter, int(v.Size), int(v.SizeRemaining),
                                   v.FileSystemLabel, strIP, GB, TB)
                got_data = bool(Data)
            except Exception:
                pass  # provider 可能不存在 / 損毀 → 進入降級流程

        # ---------------- 3‑B. Win32_Volume / LogicalDisk ----------------
        if not got_data:
            try:
                vols = cimv2.Win32_Volume(DriveType=3)
                volume_mode = True
            except Exception:
                vols = cimv2.Win32_LogicalDisk(DriveType=3)
                volume_mode = False

            for v in vols:
                drv = (v.DriveLetter if volume_mode else v.Caption) or v.Caption
                size = int(getattr(v, 'Capacity', 0) or getattr(v, 'Size', 0) or 0)
                free = int(getattr(v, 'FreeSpace', 0) or 0)
                label = getattr(v, 'Label', "") or getattr(v, 'VolumeName', "")

                # 修補：size 可能因 32‑bit 溢位只剩 2 GB → 重新計算真正容量
                if size == 0 or free > size:
                    try:
                        size = _calc_real_size(cimv2, drv)
                    except Exception:
                        pass

                _append_record(Data, drv, size, free, label, strIP, GB, TB)

        # ---------------- 4. 查詢前一天的資料 ----------------
        # 使用 API 獲取前一天的資料，用於計算變化
        try:
            # 獲取前一天的日期 (西元年格式)
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
            # print(f"查詢前一天資料: 日期={yesterday}, 伺服器={strIP}")

            # 呼叫 API 查詢前一天資料
            url = http_https() + '://' + localhost() + ':' + localport() + GetDiskInfo()
            headers = {'Content-type': 'application/json'}
            DataList = {
                'date': yesterday,
                'server_name': strIP
            }
            response = requests.post(url, data=json.dumps(DataList), headers=headers)
            # print(f"API響應: 狀態碼={response.status_code}")

            if response.status_code == 200:
                previous_data = response.json()
                if previous_data.get('message', {}).get('Code') == '0':
                    prev_disks = previous_data.get('message', {}).get('Data', [])
                    # print(f"找到前一天數據數量: {len(prev_disks)}")

                    # 根據前一天數據計算變化
                    for i, disk in enumerate(Data):
                        # 查找對應的前一天磁碟 - 修改為同時檢查IP和主機名
                        prev_disk = next((d for d in prev_disks
                                          if (d.get('ABDNNA') == disk['DnsName']
                                              or d.get('ABDNNA') == computer_name
                                              or d.get('ABDNNA') in disk['DnsName']
                                              or disk['DnsName'] in d.get('ABDNNA', ''))
                                          and d.get('ABCAPT') == disk['Caption']),
                                         None)

                        if prev_disk:
                            # print(f"找到匹配的磁碟記錄: {disk['Caption']}")
                            # 計算變化（已使用空間的變化）
                            try:
                                current_used = float(disk['FreeSpace'])  # 當前已使用空間
                                prev_used = float(prev_disk.get('ABFESP', 0))  # 前一天已使用空間

                                # 計算變化量和百分比
                                change = current_used - prev_used
                                change_percent = (change / prev_used * 100) if prev_used > 0 else 0

                                # 趨勢判斷
                                if abs(change) < 0.001:  # 極小變化視為無變化
                                    trend = '無變化'
                                elif change > 0:
                                    trend = '增加'
                                else:
                                    trend = '減少'

                                # 添加變化資訊
                                Data[i]['ChangeSize'] = f"{abs(change):.3f}"
                                Data[i]['ChangePercent'] = f"{abs(change_percent):.2f}%"
                                Data[i]['Trend'] = trend
                                # print(f"計算變化結果: {change:.3f} {disk['DiskUnit']} ({change_percent:.2f}%) - {trend}")
                            except Exception as e:
                                Data[i]['ChangeSize'] = '0.000'
                                Data[i]['ChangePercent'] = '0.00%'
                                Data[i]['Trend'] = '無法計算'
                                print(f"計算磁碟變化時出錯: {e}")
                        else:
                            print(f"沒有找到匹配的磁碟記錄: {disk['Caption']}")
                            Data[i]['ChangeSize'] = 'N/A'
                            Data[i]['ChangePercent'] = 'N/A'
                            Data[i]['Trend'] = '無前日資料'
        except Exception as e:
            print(f"獲取前一天磁碟資訊時發生錯誤: {e}")
            logging.error(f"獲取前一天磁碟資訊時發生錯誤: {e}")

        # ---------------- 5. 回傳資料 ----------------
        Ret["Data"] = Data

        # ---------------- 6. 警示邏輯（收集警示資訊但不立即發送） ----------------
        if Ret["Code"] == "0" and Data:
            alert_disks = []
            for d in Data:
                try:
                    rate = int(d["Rate"].rstrip("%"))
                    free_percent = 100 - rate
                    if free_percent < alert_threshold:
                        alert_disks.append(d)
                except Exception as e:
                    print(f"處理警示時出錯: {e}")

            # 創建伺服器資訊
            server_info = {
                "ip": strIP,
                "computer_name": computer_name,
                "all_disks": Data,  # 所有磁碟資訊
                "alert_disks": alert_disks if alert_disks else None  # 只有警告的磁碟
            }

            # 添加到所有伺服器列表
            WindowsListen.servers_info.append(server_info)

            # 如果有警告磁碟，也添加到警告列表
            if alert_disks:
                WindowsListen.servers_alert_info.append(server_info)
                # 增加待處理警告計數
                WindowsListen.pending_alerts_count += 1

                # 如果需要立即發送
                if send_alert:
                    print(f"警告：使用了立即發送選項，不建議在批次處理中使用")
                    self.sendDiskSpaceAlert(alert_threshold)

        return json.dumps(Ret, ensure_ascii=False)

    # 電腦日誌
    def getWindowsEventLog(self, ip, user, password):
        import time
        strIP, strUser, strPassword = ip, user, password

        # 初始化返回JSON格式
        Ret = {"Code": "0", "Msg": "OK", "Data": []}

        # 連線服務器
        try:
            start_time = time.time()
            getData = wmi.WMI(computer=strIP, user=strUser, password=strPassword)
            connection_time = time.time() - start_time
            print(f"WMI 連線耗時: {connection_time:.2f}秒")
        except Exception as e:
            Ret["Code"] = "-1"
            Ret["Msg"] = f"服務器 : {strIP} 連接失敗！{str(e)}"
            return json.dumps(Ret, ensure_ascii=False)

        # 設定時間範圍 - 查詢3天
        end_date = datetime.now().replace(hour=00, minute=00, second=00, microsecond=0)
        start_date = end_date - timedelta(days=3)

        print(f"getWindowsEventLog start_date: {start_date}")

        # 查詢條件
        query = f"""Select TimeGenerated, ComputerName, Type, Logfile, Message
                   from win32_NTLogEvent
                   where (Type = '錯誤' OR Type = '警告')
                   and Timegenerated >= '{start_date.strftime('%Y%m%d%H%M%S.000000-000')}'
                   and (Logfile = 'Application' OR Logfile = 'System')"""

        print(f"執行查詢...")
        # 執行查詢
        querywin32_NTLogEvent = getData.query(query)

        print(f"查詢完成")

        # 初始化數據列表和日誌狀態字典
        Data = []
        daily_logs = {(end_date - timedelta(days=i)).strftime('%Y%m%d'): False for i in range(4)}  # 恢復為4天
        kerberos_error_logged = {date: False for date in daily_logs}
        computer_name = ''
        processed_count = 0

        # 處理查詢結果並計時
        process_start_time = time.time()
        print(f"開始處理查詢結果...")

        try:
            for data in querywin32_NTLogEvent:
                processed_count += 1

                # 每處理50筆記錄顯示進度
                if processed_count % 50 == 0:
                    print(f"已處理 {processed_count} 筆記錄...")

                # 安全地轉換時間
                try:
                    time_str = data.TimeGenerated
                    if len(time_str) >= 14:
                        dt = datetime(
                            int(time_str[0:4]), int(time_str[4:6]), int(time_str[6:8]),
                            int(time_str[8:10]), int(time_str[10:12]), int(time_str[12:14]),
                            tzinfo=timezone.utc
                        )
                        local_dt = dt.astimezone()
                        date = local_dt.strftime('%Y%m%d')
                        time_formatted = local_dt.strftime('%H%M%S')
                    else:
                        # 如果時間格式不正確，跳過此記錄
                        continue
                except (ValueError, IndexError) as e:
                    print(f"時間轉換錯誤，跳過記錄: {e}")
                    continue

                if data.ComputerName:
                    computer_name = data.ComputerName

                # 創建日誌字典
                message = data.Message[:150].replace("'", '') if data.Message else "無訊息"
                if "收到 Kerberos 錯誤訊息" in (data.Message or ''):
                    if not kerberos_error_logged[date]:
                        message = data.Message[:50].replace("'", '') if data.Message else "無訊息"
                        kerberos_error_logged[date] = True
                    else:
                        continue

                log_entry = {
                    "ComputerName": computer_name,
                    "TimeGenerated_Date": f"{int(date[:4]) - 1911}{date[4:]}",
                    "TimeGenerated_Time": time_formatted,
                    "Type": data.Type,
                    "Logfile": data.Logfile,
                    "Message": message
                }

                Data.append(log_entry)

                # 標記這一天有日誌
                if date in daily_logs:
                    daily_logs[date] = True

        except Exception as e:
            print(f"處理查詢結果時發生錯誤: {e}")
            # 即使處理過程中出錯，也繼續執行

        process_time = time.time() - process_start_time
        print(f"查詢結果處理完成，耗時: {process_time:.2f}秒，處理了 {processed_count} 筆記錄")

        # 確保computer_name不為空，如果為空則設置為默認值
        if not computer_name:
            computer_name = ip_to_computer_name_map_2.get(strIP, f"{strIP}.heysong.com.tw")

        # 檢查每一天，如果沒有日誌則添加默認記錄
        for date, has_log in daily_logs.items():
            if not has_log:
                default_log = {
                    "ComputerName": computer_name,
                    "TimeGenerated_Date": f"{int(date[:4]) - 1911}{date[4:]}",
                    "TimeGenerated_Time": "000000",
                    "Type": "信息",
                    "Logfile": "Application",
                    "Message": "無錯誤或重大事件"
                }
                Data.append(default_log)

        # 按日期和時間排序
        Data.sort(key=lambda x: (x["TimeGenerated_Date"], x["TimeGenerated_Time"]))

        print(f"最終返回 {len(Data)} 筆日誌記錄")

        Ret["Data"] = Data
        return json.dumps(Ret, ensure_ascii=False)


# 示例使用方式
def write_to_log_file(log_file_path, message):
    """將錯誤訊息寫入日誌檔案"""
    with open(log_file_path, 'a', encoding='utf-8') as log_file:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_file.write(f"[{timestamp}] {message}\n")